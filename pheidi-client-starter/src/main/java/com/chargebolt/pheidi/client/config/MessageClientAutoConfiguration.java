package com.chargebolt.pheidi.client.config;

import com.chargebolt.pheidi.client.MessageClient;
import com.chargebolt.pheidi.client.impl.DefaultMessageClient;
import com.chargebolt.pheidi.client.remote.AppMonitorApi;
import com.chargebolt.pheidi.client.template.MessageTemplateClient;
import com.chargebolt.pheidi.client.util.MessageSender;

import feign.Feign;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PreDestroy;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 消息客户端自动配置
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(MessageClientProperties.class)
@Import({MessageTemplateClient.class, Feign2xAutoConfiguration.class})
public class MessageClientAutoConfiguration {

    private ThreadPoolTaskExecutor messageThreadPoolExecutor;

    /**
     * 配置消息客户端线程池
     */
    @Bean("messageClientExecutor")
    public Executor messageClientExecutor(MessageClientProperties properties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(properties.getThreadPool().getCorePoolSize());
        executor.setMaxPoolSize(properties.getThreadPool().getMaxPoolSize());
        executor.setQueueCapacity(properties.getThreadPool().getQueueCapacity());
        executor.setThreadNamePrefix(properties.getThreadPool().getThreadNamePrefix());
        // 拒绝策略：丢弃任务并记录日志，避免阻塞调用线程
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                super.rejectedExecution(r, e);
                // 记录被拒绝的任务
                log.warn("消息发送任务被拒绝执行，线程池已满: activeCount={}, poolSize={}, queueSize={}",
                    e.getActiveCount(), e.getPoolSize(), e.getQueue().size());
            }
        });
        executor.initialize();
        
        // 保存引用以便在销毁时关闭
        this.messageThreadPoolExecutor = executor;
        
        return executor;
    }

    /**
     * 配置消息客户端
     */
    @Bean
    @ConditionalOnMissingBean
    public MessageClient messageClient(MessageClientProperties properties, 
                                      AppMonitorApi appMonitorApi,
                                      Executor messageClientExecutor) {
        DefaultMessageClient client = new DefaultMessageClient(properties, appMonitorApi, messageClientExecutor);
        // 初始化静态工具类
        MessageSender.init(client);
        return client;
    }
    
    /**
     * 应用关闭时优雅关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        if (messageThreadPoolExecutor != null) {
            messageThreadPoolExecutor.shutdown();
        }
    }
}
