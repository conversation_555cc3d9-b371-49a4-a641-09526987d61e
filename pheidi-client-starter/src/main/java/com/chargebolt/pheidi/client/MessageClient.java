package com.chargebolt.pheidi.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 消息客户端接口
 */
public interface MessageClient {

    ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 发送消息
     *
     * @param module  模块
     * @param subject 主题
     * @param errMsg  错误信息
     * @param params  消息参数
     * @return 发送结果
     */
    CompletableFuture<Boolean> sendMessage(String module, String subject, String errMsg, Map<String, String> params);

    /**
     * 发送消息（使用对象参数）
     * 
     * @param module  模块
     * @param subject 主题
     * @param errMsg  错误信息
     * @param params  对象参数，将自动提取字段名和值作为消息参数
     * @return 发送结果
     */
    default CompletableFuture<Boolean> sendMessage(String module, String subject, String errMsg, Object params) {
        return sendMessage(module, subject, errMsg, convertObjectToMap(params));
    }

    /**
     * 发送消息（带重试，使用对象参数）
     * 
     * @param module  模块
     * @param subject 主题
     * @param errMsg  错误信息
     * @param params  对象参数，将自动提取字段名和值作为消息参数
     * @return 发送结果
     */
    default CompletableFuture<Boolean> sendMessageWithRetry(String module, String subject, String errMsg,
            Object params) {
        return sendMessageWithRetry(module, subject, errMsg, convertObjectToMap(params));
    }

    /**
     * 将对象转换为Map<String, String>
     * 
     * @param obj 要转换的对象
     * @return 包含对象有值字段的Map，key为字段名，value为字段值的字符串表示
     */
    default Map<String, String> convertObjectToMap(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        
        try {
            // 使用 Jackson 将对象序列化为 JSON 字符串
            String jsonString = OBJECT_MAPPER.writeValueAsString(obj);
            
            // 将 JSON 字符串反序列化为 Map<String, Object>
            Map<String, Object> objectMap = OBJECT_MAPPER.readValue(jsonString, 
                new TypeReference<Map<String, Object>>() {});
            
            // 将 Map 中的值转换为字符串
            Map<String, String> stringMap = new HashMap<>();
            objectMap.forEach((key, value) -> {
                if (value != null) {
                    try {
                        stringMap.put(key, OBJECT_MAPPER.writeValueAsString(value));
                    } catch (JsonProcessingException e) {
                        // 处理异常，这里简单地跳过有问题的字段
                        // 在实际应用中可能需要更复杂的错误处理
                    }
                }
            });
            
            return stringMap;
        } catch (JsonProcessingException e) {
            // 处理异常，这里简单地返回空 Map
            // 在实际应用中可能需要更复杂的错误处理
            return new HashMap<>();
        }
    }
}
