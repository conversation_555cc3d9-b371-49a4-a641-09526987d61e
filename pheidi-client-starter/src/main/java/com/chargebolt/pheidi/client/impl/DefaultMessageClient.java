package com.chargebolt.pheidi.client.impl;

import com.alibaba.fastjson.JSON;
import com.chargebolt.pheidi.client.MessageClient;
import com.chargebolt.pheidi.client.config.MessageClientProperties;
import com.chargebolt.pheidi.client.exception.MessageClientException;
import com.chargebolt.pheidi.client.remote.AppMonitorApi;
import com.chargebolt.pheidi.dto.LogMsgDTO;
import com.chargebolt.pheidi.dto.PheidiResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestClientException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 消息客户端默认实现
 */
@Slf4j
public class DefaultMessageClient implements MessageClient {

    private final MessageClientProperties properties;
    private final AppMonitorApi appMonitorApi;
    private final Executor executor;

    public DefaultMessageClient(MessageClientProperties properties,
                                AppMonitorApi appMonitorApi,
                                Executor executor) {
        this.properties = properties;
        this.appMonitorApi = appMonitorApi;
        this.executor = executor;
    }

    @Override
    public CompletableFuture<Boolean> sendMessage(String module, String subject, String errMsg, Map<String, String> params) {
        if (properties.isLogEnabled()) {
            log.info("开始发送消息: module={}, subject={}, errMsg={}, params={}", module, subject, errMsg, params);
        }
        // 构建消息体
        LogMsgDTO messageBody = buildLogMsgBody(module, subject, errMsg, params);

        // 使用 CompletableFuture.supplyAsync 确保真正异步执行，并捕获异常
        return CompletableFuture.supplyAsync(() -> {
            try {
                return doSendMessage(messageBody);
            } catch (Exception e) {
                // 异步执行中的异常不会传播到调用方，只记录日志
                log.error("异步发送消息失败: module={}, subject={}, error={}", module, subject, e.getMessage(), e);
                return false;
            }
        }, executor);
    }

    @Override
    public CompletableFuture<Boolean> sendMessageWithRetry(String module, String subject, String errMsg, Map<String, String> params) {
        if (properties.isLogEnabled()) {
            log.info("开始发送消息（带重试）: module={}, subject={}, errMsg={}, params={}", module, subject, errMsg, params);
        }
        // 构建消息体
        LogMsgDTO messageBody = buildLogMsgBody(module, subject, errMsg, params);

        // 使用 CompletableFuture.supplyAsync 确保真正异步执行，并手动实现重试逻辑
        return CompletableFuture.supplyAsync(() -> {
            int maxRetries = properties.getMaxRetries();
            long retryInterval = properties.getRetryInterval();

            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    boolean result = doSendMessage(messageBody);
                    if (result) {
                        if (properties.isLogEnabled() && attempt > 1) {
                            log.info("消息发送成功，重试次数: {}/{}", attempt, maxRetries);
                        }
                        return true;
                    } else {
                        if (attempt < maxRetries) {
                            log.warn("消息发送失败，准备重试: {}/{}", attempt, maxRetries);
                        }
                    }
                } catch (RestClientException | MessageClientException e) {
                    if (attempt < maxRetries) {
                        log.warn("消息发送异常，准备重试: {}/{}, error: {}", attempt, maxRetries, e.getMessage());
                        try {
                            Thread.sleep(retryInterval);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.error("重试等待被中断: {}", ie.getMessage());
                            return false;
                        }
                    } else {
                        log.error("消息发送最终失败，已达最大重试次数: {}, error: {}", maxRetries, e.getMessage(), e);
                    }
                } catch (Exception e) {
                    // 其他异常不重试
                    log.error("消息发送遇到不可重试异常: {}", e.getMessage(), e);
                    return false;
                }
            }

            // 所有重试都失败
            log.error("消息发送最终失败: module={}, subject={}, 重试次数: {}", module, subject, maxRetries);
            return false;
        }, executor);
    }

    private LogMsgDTO buildLogMsgBody(String module, String subject, String errMsg, Map<String, String> params) {
        LogMsgDTO messageBody = new LogMsgDTO();
        messageBody.setModule(module);
        messageBody.setSubject(subject);
        messageBody.setLogMsg(errMsg);
        messageBody.setAppName(properties.getAppName());
        messageBody.setLogTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        messageBody.setExtra(params);
        return messageBody;
    }

    /**
     * 执行消息发送
     */
    private boolean doSendMessage(LogMsgDTO messageBody) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("消息发送");
        try {
            PheidiResponse response = appMonitorApi.logPush(messageBody);
            if (response != null && "0".equals(response.getCode())) {
                return true;
            }
            log.warn("消息发送失败: result = {}", JSON.toJSONString(response));
            return false;
        } catch (Exception e) {
            log.error("消息发送请求异常: {}", e.getMessage(), e);
            throw new MessageClientException("消息发送请求异常", e);
        } finally {
            stopWatch.stop();
            if (properties.isLogEnabled()) {
                log.info("消息发送耗时: {}ms", stopWatch.getTotalTimeMillis());
            }
        }

    }
}
